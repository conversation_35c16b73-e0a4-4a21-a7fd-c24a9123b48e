#!/usr/bin/env python3
"""
Setup script for TradingAgents API keys
Run this script to set up your environment variables
"""

import os
import sys

def setup_environment():
    """Set up environment variables for TradingAgents"""
    
    print("=== TradingAgents Environment Setup ===")
    print()
    
    # Check if keys are already set
    openai_key = os.getenv('OPENAI_API_KEY')
    finnhub_key = os.getenv('FINNHUB_API_KEY')
    
    if openai_key and finnhub_key:
        print("✅ API keys are already configured!")
        print(f"OpenAI API Key: {openai_key[:10]}...")
        print(f"FinnHub API Key: {finnhub_key[:10]}...")
        return True
    
    print("❌ API keys not found. Please set them up:")
    print()
    
    # Get OpenAI API Key
    if not openai_key:
        print("1. Get your OpenAI API key from: https://platform.openai.com/api-keys")
        openai_key = input("Enter your OpenAI API key: ").strip()
        if openai_key:
            os.environ['OPENAI_API_KEY'] = openai_key
            print("✅ OpenAI API key set!")
        else:
            print("❌ OpenAI API key is required!")
            return False
    
    # Get FinnHub API Key
    if not finnhub_key:
        print("2. Get your FinnHub API key from: https://finnhub.io/ (free tier available)")
        finnhub_key = input("Enter your FinnHub API key: ").strip()
        if finnhub_key:
            os.environ['FINNHUB_API_KEY'] = finnhub_key
            print("✅ FinnHub API key set!")
        else:
            print("❌ FinnHub API key is required!")
            return False
    
    print()
    print("🎉 Environment setup complete!")
    print("You can now run: python main.py")
    return True

def main():
    """Main function"""
    if setup_environment():
        print("\n=== Testing Configuration ===")
        try:
            from tradingagents.graph.trading_graph import TradingAgentsGraph
            from tradingagents.default_config import DEFAULT_CONFIG
            
            print("✅ TradingAgents imports successful!")
            
            # Test with minimal config
            config = DEFAULT_CONFIG.copy()
            config["max_debate_rounds"] = 1
            config["online_tools"] = False  # Use cached data for testing
            
            print("✅ Configuration created successfully!")
            print("Ready to run TradingAgents!")
            
        except ImportError as e:
            print(f"❌ Import error: {e}")
            print("Make sure you have installed all dependencies: pip install -r requirements.txt")
        except Exception as e:
            print(f"❌ Error: {e}")
    else:
        print("\n❌ Setup failed. Please try again.")
        sys.exit(1)

if __name__ == "__main__":
    main() 