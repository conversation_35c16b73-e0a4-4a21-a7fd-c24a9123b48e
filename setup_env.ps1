# TradingAgents Environment Setup Script for Windows PowerShell
# Run this script to set up your API keys

Write-Host "=== TradingAgents Environment Setup ===" -ForegroundColor Green
Write-Host ""

# Check if keys are already set
$openai_key = $env:OPENAI_API_KEY
$finnhub_key = $env:FINNHUB_API_KEY

if ($openai_key -and $finnhub_key) {
    Write-Host "✅ API keys are already configured!" -ForegroundColor Green
    Write-Host "OpenAI API Key: $($openai_key.Substring(0,10))..." -ForegroundColor Yellow
    Write-Host "FinnHub API Key: $($finnhub_key.Substring(0,10))..." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "You can now run: python main.py" -ForegroundColor Green
    exit 0
}

Write-Host "❌ API keys not found. Please set them up:" -ForegroundColor Red
Write-Host ""

# Get OpenAI API Key
if (-not $openai_key) {
    Write-Host "1. Get your OpenAI API key from: https://platform.openai.com/api-keys" -ForegroundColor Cyan
    $openai_key = Read-Host "Enter your OpenAI API key"
    if ($openai_key) {
        $env:OPENAI_API_KEY = $openai_key
        Write-Host "✅ OpenAI API key set!" -ForegroundColor Green
    } else {
        Write-Host "❌ OpenAI API key is required!" -ForegroundColor Red
        exit 1
    }
}

# Get FinnHub API Key
if (-not $finnhub_key) {
    Write-Host "2. Get your FinnHub API key from: https://finnhub.io/ (free tier available)" -ForegroundColor Cyan
    $finnhub_key = Read-Host "Enter your FinnHub API key"
    if ($finnhub_key) {
        $env:FINNHUB_API_KEY = $finnhub_key
        Write-Host "✅ FinnHub API key set!" -ForegroundColor Green
    } else {
        Write-Host "❌ FinnHub API key is required!" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "🎉 Environment setup complete!" -ForegroundColor Green
Write-Host "You can now run: python main.py" -ForegroundColor Green
Write-Host ""
Write-Host "Note: These environment variables are only set for this session." -ForegroundColor Yellow
Write-Host "To make them permanent, add them to your Windows Environment Variables." -ForegroundColor Yellow 