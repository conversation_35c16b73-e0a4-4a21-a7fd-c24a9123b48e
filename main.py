import os
import sys
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

def check_api_keys():
    """Check if required API keys are set"""
    openai_key = os.getenv('OPENAI_API_KEY')
    finnhub_key = os.getenv('FINNHUB_API_KEY')
    
    if not openai_key:
        print("❌ Error: OPENAI_API_KEY environment variable not set!")
        print("Please set your OpenAI API key:")
        print("1. Get your key from: https://platform.openai.com/api-keys")
        print("2. Set it as: $env:OPENAI_API_KEY='your_key_here' (PowerShell)")
        print("   or run: python setup_env.py")
        return False
    
    if not finnhub_key:
        print("❌ Error: FINNHUB_API_KEY environment variable not set!")
        print("Please set your FinnHub API key:")
        print("1. Get your key from: https://finnhub.io/ (free tier available)")
        print("2. Set it as: $env:FINNHUB_API_KEY='your_key_here' (PowerShell)")
        print("   or run: python setup_env.py")
        return False
    
    return True

def main():
    """Main function"""
    print("=== TradingAgents Demo ===")
    print()
    
    # Check API keys
    if not check_api_keys():
        sys.exit(1)
    
    # Create a cost-effective config
    config = DEFAULT_CONFIG.copy()
    config["deep_think_llm"] = "gpt-4o-mini"  # Use cheaper model
    config["quick_think_llm"] = "gpt-4o-mini"  # Use cheaper model
    config["max_debate_rounds"] = 1  # Limit debate rounds to save costs
    config["online_tools"] = False  # Use cached data to avoid API calls
    
    print("✅ Configuration loaded successfully!")
    print(f"Using models: {config['deep_think_llm']} and {config['quick_think_llm']}")
    print(f"Debate rounds: {config['max_debate_rounds']}")
    print(f"Online tools: {config['online_tools']}")
    print()
    
    try:
        # Initialize with custom config
        print("Initializing TradingAgents...")
        ta = TradingAgentsGraph(debug=True, config=config)
        print("✅ TradingAgents initialized successfully!")
        
        # Forward propagate
        print("\n=== Running Analysis ===")
        print("Analyzing NVDA for 2024-05-10...")
        _, decision = ta.propagate("NVDA", "2024-05-10")
        
        print("\n=== Final Decision ===")
        print(decision)
        
    except Exception as e:
        print(f"❌ Error during execution: {e}")
        print("This might be due to:")
        print("- Invalid API keys")
        print("- Network connectivity issues")
        print("- Insufficient API credits")
        sys.exit(1)

if __name__ == "__main__":
    main()
